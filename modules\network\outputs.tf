output "virtual_network_id" {
  value       = azurerm_virtual_network.main.id
  description = "The ID of the virtual network"
}

output "virtual_network_name" {
  value       = azurerm_virtual_network.main.name
  description = "The name of the virtual network"
}

output "subnet_ids" {
  value       = { for name, subnet in azurerm_subnet.subnets : name => subnet.id }
  description = "Map of subnet names to their IDs"
}

output "subnet_names" {
  value       = { for name, subnet in azurerm_subnet.subnets : name => subnet.name }
  description = "Map of subnet names"
}

output "nsg_ids" {
  description = "IDs of the Network Security Groups"
  value       = { for nsg, config in azurerm_network_security_group.this : nsg => config.id }
}
