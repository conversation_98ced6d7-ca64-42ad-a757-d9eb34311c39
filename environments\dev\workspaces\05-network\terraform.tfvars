location             = "eastus"
virtual_network_name = "ar-az-est1-d-cps-vnet-001"
address_space        = ["***********/27"]
dns_servers          = ["***********", "***********"]
resource_group_name  = "ar-az-est1-d-cps-rg-001"
subnets = [
  {
    name           = "ar-az-est1-cps-sn-001"
    address_prefix = "***********/28"
    delegation     = "Microsoft.Web/serverFarms"
  },
  {
    name           = "ar-az-est1-cps-sn-002"
    address_prefix = "***********/28"
  },
]

nsg_configs = []

tags = {
  accountnumber = "3682.301700"
  environment   = "dev"
  owner         = "<EMAIL>"
  profitcenter  = "*********"
  project       = "cps"
  terraform     = "true"
}
