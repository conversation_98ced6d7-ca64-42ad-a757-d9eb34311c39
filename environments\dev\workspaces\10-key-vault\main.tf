# Get the outputs from the webapp workspace
data "terraform_remote_state" "webapp" {
  backend = "azurerm"
  config = {
    resource_group_name  = "ar-az-est1-tfstate-rg"
    storage_account_name = "arazest1tfstatedev"
    container_name       = "tfstate"
    key                  = "terraform.tfstate"
  }
  workspace = "20-webapp"
}

locals {
  web_app_principal_ids = try(data.terraform_remote_state.webapp.outputs.module.webapp.web_app_principal_ids, {})

  # Create access policies for each web app
  access_policies = [
    for name, principal_id in local.web_app_principal_ids : {
      object_id               = principal_id
      tenant_id               = var.tenant_id
      key_permissions         = ["Get", "List"]
      secret_permissions      = ["Get", "List"]
      certificate_permissions = []
      storage_permissions     = []
    }
  ]
}

module "key_vault" {
  source = "../../../../modules/key-vault"

  key_vault_name           = var.key_vault_name
  location                 = var.location
  resource_group_name      = var.resource_group_name
  sku_name                 = var.key_vault_sku_name
  tenant_id                = var.tenant_id
  tags                     = var.tags
  access_policies          = local.access_policies
  purge_protection_enabled = false
}
