resource "azurerm_redis_cache" "redis" {
  name                = var.redis_name
  location            = var.location
  resource_group_name = var.resource_group_name
  capacity            = var.capacity
  family              = var.family
  sku_name            = var.sku_name
  minimum_tls_version = var.minimum_tls_version
  non_ssl_port_enabled = false
  redis_version       = "6"
  public_network_access_enabled = true

  redis_configuration {
    maxmemory_reserved = var.maxmemory_reserved
    maxmemory_delta    = var.maxmemory_delta
    maxfragmentationmemory_reserved = var.maxmemory_delta
  }

  tags = var.tags
} 