output "web_app_urls" {
  value       = { for name, app in azurerm_windows_web_app.main : name => app.default_hostname }
  description = "Map of web app names to their URLs"
}

output "web_app_ids" {
  value       = { for name, app in azurerm_windows_web_app.main : name => app.id }
  description = "Map of web app names to their IDs"
}

output "web_app_principal_ids" {
  value       = { for name, app in azurerm_windows_web_app.main : name => app.identity[0].principal_id }
  description = "Map of web app names to their managed identity principal IDs"
}
