module "letsencrypt_automation" {
  source = "../../../../modules/letsencrypt-automation"

  resource_group_name            = var.resource_group_name
  location                       = var.location
  function_app_name              = var.function_app_name
  function_app_service_plan_name = var.function_app_service_plan_name
  function_storage_account_name  = var.function_storage_account_name

  # Azure resource references
  key_vault_name            = var.key_vault_name
  application_insights_name = var.application_insights_name
  application_gateway_name  = var.application_gateway_name

  # Let's Encrypt configuration
  acme_directory_url              = var.acme_directory_url
  acme_email                      = var.acme_email
  certificate_renewal_days        = var.certificate_renewal_days
  domains                         = var.domains
  static_website_storage_accounts = var.static_website_storage_accounts

  tags = var.tags
}
