application_gateway_name = "ar-az-est1-d-cps-agw-001"
location                 = "eastus"
resource_group_name      = "ar-az-est1-d-cps-rg-001"
public_ip_name           = "ar-az-est1-d-cps-agw-pip-001"
sku_name                 = "WAF_v2"
sku_tier                 = "WAF_v2"
# Using autoscaling instead of fixed capacity
min_capacity      = 2
max_capacity      = 10
appgw_subnet_name = "ar-az-est1-cps-sn-002"

domains = {
  "dev-app.ecps.ca" = {
    app_backend = {
      host_name   = "arazest1dadminst001.z13.web.core.windows.net"
      type        = "storage"
      health_path = "/"
    }
    api_backend = {
      host_name   = "ar-az-est1-d-cpsadmin-web-001.azurewebsites.net"
      type        = "webapp"
      health_path = "/" # Temporary: use root path until /api/health is implemented
    }
  }
  "dev-supplier.ecps.ca" = {
    app_backend = {
      host_name   = "arazest1dsupplierst001.z13.web.core.windows.net"
      type        = "storage"
      health_path = "/"
    }
    api_backend = {
      host_name   = "ar-az-est1-d-cpssupplier-web-001.azurewebsites.net"
      type        = "webapp"
      health_path = "/" # Temporary: use root path until /api/health is implemented
    }
  }
}

tags = {
  accountnumber = "3682.301700"
  environment   = "dev"
  owner         = "<EMAIL>"
  profitcenter  = "*********"
  project       = "cps"
  terraform     = "true"
}
