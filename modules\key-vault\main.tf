resource "azurerm_key_vault" "main" {
  name                     = var.key_vault_name
  location                 = var.location
  resource_group_name      = var.resource_group_name
  sku_name                 = var.sku_name
  tenant_id                = var.tenant_id
  tags                     = var.tags
  purge_protection_enabled = var.purge_protection_enabled
  enable_rbac_authorization = var.enable_rbac_authorization
}

resource "azurerm_key_vault_access_policy" "access_policies" {
  count        = length(var.access_policies)
  key_vault_id = azurerm_key_vault.main.id
  tenant_id    = var.access_policies[count.index].tenant_id
  object_id    = var.access_policies[count.index].object_id

  key_permissions         = var.access_policies[count.index].key_permissions
  secret_permissions      = var.access_policies[count.index].secret_permissions
  certificate_permissions = var.access_policies[count.index].certificate_permissions
  storage_permissions     = var.access_policies[count.index].storage_permissions
}
