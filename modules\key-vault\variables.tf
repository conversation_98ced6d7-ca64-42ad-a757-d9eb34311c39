variable "key_vault_name" {
  description = "Name of the Key Vault"
  type        = string
}

variable "location" {
  description = "Azure region"
  type        = string
}

variable "resource_group_name" {
  description = "Name of the resource group"
  type        = string
}


variable "sku_name" {
  description = "SKU name for the Key Vault"
  type        = string
  default     = "standard"
}

variable "tenant_id" {
  description = "Azure Active Directory tenant ID"
  type        = string
}

variable "tags" {
  description = "Tags to apply to Application Insights"
  type        = map(string)
  default     = {}
}

variable "access_policies" {
  description = "List of access policy configurations for the Key Vault"
  type = list(object({
    object_id               = string
    tenant_id               = string
    key_permissions         = list(string)
    secret_permissions      = list(string)
    certificate_permissions = list(string)
    storage_permissions     = list(string)
  }))
  default = []
}

variable "purge_protection_enabled" {
  description = "Enable purge protection"
  type        = bool
  default     = false
}

variable "enable_rbac_authorization" {
  description = "Enable RBAC authorization"
  type        = bool
  default     = false
}
