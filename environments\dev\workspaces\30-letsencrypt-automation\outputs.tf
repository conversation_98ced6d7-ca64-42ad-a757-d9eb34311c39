output "function_app_name" {
  description = "Name of the Let's Encrypt automation Function App"
  value       = module.letsencrypt_automation.function_app_name
}

output "function_app_id" {
  description = "ID of the Let's Encrypt automation Function App"
  value       = module.letsencrypt_automation.function_app_id
}

output "function_app_default_hostname" {
  description = "Default hostname of the Function App"
  value       = module.letsencrypt_automation.function_app_default_hostname
}

output "function_app_principal_id" {
  description = "Principal ID of the Function App managed identity"
  value       = module.letsencrypt_automation.function_app_principal_id
}

output "acme_challenges_urls" {
  description = "Map of domains to their ACME challenges URLs"
  value       = module.letsencrypt_automation.acme_challenges_urls
}

output "static_website_storage_accounts" {
  description = "Map of domains to their static website storage account names"
  value       = module.letsencrypt_automation.static_website_storage_accounts
}
