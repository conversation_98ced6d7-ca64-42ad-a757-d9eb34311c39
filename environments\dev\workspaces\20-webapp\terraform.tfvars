subnet_id             = "/subscriptions/91c43315-5ccf-47d1-85dc-c73e8579b055/resourceGroups/ar-az-est1-d-cps-rg-001/providers/Microsoft.Network/virtualNetworks/ar-az-est1-d-cps-vnet-001/subnets/ar-az-est1-cps-sn-001"
service_plan_name     = "ar-az-est1-d-cps-asp-001"  # App Service Plan name
sku_name              = "B2"
resource_group_name   = "ar-az-est1-d-cps-rg-001"
location              = "eastus"
environment           = "dev"
os_type               = "Windows"
web_apps = [
  {
    name = "ar-az-est1-d-cpsadmin-web-001"
    cors_config = {
      allowed_origins = [
        "https://arazest1dadminst001.z13.web.core.windows.net",
        "https://dev-app.ecps.ca"
      ]
      support_credentials = true
    }
    app_settings = {
      "Auth0:Audience" = "https://administrationportal/api"
    }
  },
  {
    name = "ar-az-est1-d-cpssupplier-web-001"
    cors_config = {
      allowed_origins = [
        "https://arazest1dsupplierst001.z13.web.core.windows.net",
        "https://dev-supplier.ecps.ca"
      ]
      support_credentials = true
    }
    app_settings = {
      "Auth0:Audience" = "https://supplierportal/api"
    }
  }
]

storage_accounts = [
  {
    name                          = "arazest1dadminst001"
    resource_group_name           = "ar-az-est1-d-cps-rg-001"
    location                      = "eastus"
    account_tier                  = "Standard"
    static_website_index_document = "index.html"
    account_replication_type      = "LRS"
    tags = {
      accountnumber = "3682.301700"
      criticality   = "high"
      environment   = "dev"
      owner         = "<EMAIL>"
      profitcenter  = "*********"
      project       = "cps"
      terraform     = "true"
    }
    }, {
    name                          = "arazest1dsupplierst001"
    resource_group_name           = "ar-az-est1-d-cps-rg-001"
    location                      = "eastus"
    account_tier                  = "Standard"
    static_website_index_document = "index.html"
    account_replication_type      = "LRS"
    tags = {
      accountnumber = "3682.301700"
      criticality   = "high"
      environment   = "dev"
      owner         = "<EMAIL>"
      profitcenter  = "*********"
      project       = "cps"
      terraform     = "true"
    }
  }
]

tags = {
  accountnumber = "3682.301700"
  criticality   = "high"
  environment   = "dev"
  owner         = "<EMAIL>"
  profitcenter  = "*********"
  project       = "cps"
  terraform     = "true"
}
