output "storage_account_ids" {
  description = "A map of storage account names to their IDs"
  value = {
    for name, sa in azurerm_storage_account.main :
    name => sa.id
  }
}

output "storage_account_names" {
  description = "A list of all created storage account names"
  value = [
    for sa in azurerm_storage_account.main :
    sa.name
  ]
}

output "primary_web_endpoints" {
  description = "A map of storage account names to their primary web endpoints"
  value = {
    for name, sa in azurerm_storage_account.main :
    name => sa.primary_web_endpoint
  }
}
