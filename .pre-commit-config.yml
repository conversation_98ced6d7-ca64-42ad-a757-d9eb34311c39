# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict

  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.86.0
    hooks:
      - id: terraform_fmt
      - id: terraform_validate
      # - id: terraform_docs
      - id: terraform_tflint
        args:
          - --args=--config=__GIT_WORKING_DIR__/.tflint.hcl
      # - id: terraform_checkov
      #   args:
      #     - --args=--quiet
      #     - --args=--framework=terraform
      # - id: terrascan
      #   args:
      #     - --args=--non-recursive
      #     - --args=--scan-type=terraform

  # - repo: https://github.com/bridgecrewio/checkov.git
  #   rev: 3.1.67
  #   hooks:
  #     - id: checkov
  #       args:
  #         - --quiet
  #         - --framework=terraform
  #         - --skip-check=CKV_AWS_1,CKV_AZURE_41
  #         - --output=cli

  - repo: https://github.com/zricethezav/gitleaks
    rev: v8.18.1
    hooks:
      - id: gitleaks