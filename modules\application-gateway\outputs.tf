output "application_gateway_id" {
  description = "ID of the Application Gateway"
  value       = azurerm_application_gateway.appgw.id
}

output "application_gateway_frontend_ip" {
  description = "Frontend IP of the Application Gateway"
  value       = azurerm_public_ip.appgw_pip.ip_address
}

output "application_gateway_identity_principal_id" {
  description = "Principal ID of the Application Gateway managed identity"
  value       = azurerm_user_assigned_identity.appgw_identity.principal_id
}

output "application_gateway_identity_tenant_id" {
  description = "Tenant ID of the Application Gateway managed identity"
  value       = azurerm_user_assigned_identity.appgw_identity.tenant_id
}

output "application_gateway_identity_id" {
  description = "ID of the Application Gateway UserAssigned managed identity"
  value       = azurerm_user_assigned_identity.appgw_identity.id
}
