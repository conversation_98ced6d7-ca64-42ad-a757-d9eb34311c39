variable "subnet_id" {
  description = "The ID of the subnet to be integrated with the web app"
  type        = string
}

variable "web_apps" {
  description = "List of web apps to be created"
  type = list(object({
    name = string
    cors_config = optional(object({
      allowed_origins     = list(string)
      support_credentials = bool
    }))
    app_settings = optional(map(string), {})
  }))
}

variable "service_plan_name" {
  description = "Name of the App Service Plan"
  type        = string

}

variable "sku_name" {
  description = "SKU name for the App Service Plan"
  type        = string
  default     = "S1"

}

variable "environment" {
  description = "Environment for the web app (e.g., dev, test, prod)"
  type        = string
  default     = "qa"
}

variable "location" {
  description = "The Azure location where the resources will be created."
  type        = string
  default     = "East US"
}

variable "resource_group_name" {
  description = "Name of the resource group"
  type        = string
}

variable "tags" {
  type        = map(string)
  description = "Tags to be applied to the resources"
  default     = {}
}

variable "os_type" {
  description = "The OS type for the web app (Linux or Windows)"
  type        = string
  default     = "Windows"
}


variable "storage_accounts" {
  description = "List of storage accounts to create"
  type = list(object({
    name                          = string
    resource_group_name           = string
    location                      = string
    account_tier                  = string
    account_replication_type      = string
    static_website_index_document = string
    tags                          = map(string)
  }))
}
