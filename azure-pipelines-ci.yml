name: Terraform-CI-Pipeline

trigger:
  branches:
    include:
      - main

pr:
  branches:
    include:
      - main

pool:
  vmImage: 'ubuntu-latest'

variables:
  - name: TF_VERSION
    value: '1.6.1'
  - name: WORKING_DIR
    value: '$(System.DefaultWorkingDirectory)'

stages:
- stage: DetectChanges
  jobs:
  - job: IdentifyChangedWorkspaces
    steps:
    - checkout: self
      fetchDepth: 0

    - bash: |
        # Get changed files between the last two commits
        changed_files=$(git diff --name-only HEAD^ HEAD)

        # Use an associative array to track unique environment/workspace combinations.
        declare -A workspace_set

        # Flag to indicate if any workspace is affected
        has_workspace='false'

        # Process changed files to detect direct workspace changes.
        # This will match paths like: environments/<env>/workspaces/<workspace>/...
        while IFS= read -r file; do
          if [[ "$file" =~ ^environments/([^/]+)/workspaces/([^/]+)/ ]]; then
            env="${BASH_REMATCH[1]}"
            ws="${BASH_REMATCH[2]}"
            # Check if the workspace directory actually exists
            if [ -d "environments/$env/workspaces/$ws" ]; then
              key="${env}/${ws}"
              # Insert into associative array; duplicate keys will be overwritten
              workspace_set["$key"]="$env:$ws"
              has_workspace='true'
            else
              echo "Warning: Referenced workspace $ws does not exist, skipping."
            fi
          fi
        done <<< "$changed_files"

        # If any file under modules/ changed, check all workspaces for module references.
        if echo "$changed_files" | grep -q "^modules/"; then
          # Loop through each environment folder
          for env_dir in environments/*; do
            if [ -d "$env_dir" ]; then
              # Loop through each workspace within the environment
              for ws_dir in "$env_dir"/workspaces/*; do
                if [ -d "$ws_dir" ] && [ -f "$ws_dir/main.tf" ]; then
                  # If the workspace's main.tf references a module from modules/
                  if grep -q "module.*source.*modules/" "$ws_dir/main.tf"; then
                    env=$(basename "$env_dir")
                    ws=$(basename "$ws_dir")
                    # Double-check that the workspace directory exists (should always be true here, but being cautious)
                    if [ -d "environments/$env/workspaces/$ws" ]; then
                      key="${env}/${ws}"
                      workspace_set["$key"]="$env:$ws"
                      has_workspace='true'
                    else
                      echo "Warning: Workspace directory $ws unexpectedly does not exist, skipping."
                    fi
                  fi
                fi
              done
            fi
          done
        fi

        # Build the JSON object using only the unique keys from the associative array.
        json_object="{"
        first=true
        KEYS=$(echo "${!workspace_set[@]}" | tr ' ' '\012' | sort | tr '\012' ' ')
        for key in ${KEYS}; do
          if [ "$first" = true ]; then
            first=false
          else
            json_object="$json_object,"
          fi
          # Split the stored value (format "env:workspace") into separate variables.
          IFS=":" read env ws <<< "${workspace_set[$key]}"
          json_object="$json_object\"$key\":{\"environment\":\"$env\",\"workspace\":\"$ws\"}"
        done
        json_object="$json_object}"

        # Set Azure DevOps variables using logging commands.
        echo "##vso[task.setvariable variable=HAS_WORKSPACE;isOutput=true]$has_workspace"
        echo "##vso[task.setvariable variable=WORKSPACE_MATRIX;isOutput=true]$json_object"

        # Log the JSON and flag.
        echo "$json_object"
        echo "Has workspace: $has_workspace"
      name: detectChanges

- stage: Validate
  displayName: 'Validate Infrastructure Changes'
  dependsOn: DetectChanges
  condition: eq(dependencies.DetectChanges.outputs['IdentifyChangedWorkspaces.detectChanges.HAS_WORKSPACE'], 'true')
  jobs:
  - job: ValidateWorkspaces
    displayName: 'Validate Terraform Workspaces'
    variables:
      matrix: $[ stageDependencies.DetectChanges.IdentifyChangedWorkspaces.outputs['detectChanges.WORKSPACE_MATRIX'] ]
    strategy:
      matrix: $[ variables.matrix ]
    steps:
      - checkout: self

      - bash: |
          echo "##[section]Configuring service connection based on environment changes"
          echo "Environment with changes: $(environment)"
          echo "Workspace: $(workspace)"

          # Set service connection and storage account based on environment from change detection
          if [ "$(environment)" = "prod" ]; then
            echo "##vso[task.setvariable variable=serviceConnection]AzureDevops-Pipelines-Prod"
            echo "##vso[task.setvariable variable=tfStateStorageAccount]arazest1tfstateprod"
            echo "Using PRODUCTION service principal: AzureDevops-Pipelines-Prod"
          elif [ "$(environment)" = "dev" ]; then
            echo "##vso[task.setvariable variable=serviceConnection]AzureDevops-Pipelines"
            echo "##vso[task.setvariable variable=tfStateStorageAccount]arazest1tfstatedev"
            echo "Using DEV service principal: AzureDevops-Pipelines"
          elif [ "$(environment)" = "qa" ]; then
            echo "##vso[task.setvariable variable=serviceConnection]AzureDevops-Pipelines"
            echo "##vso[task.setvariable variable=tfStateStorageAccount]arazest1tfstateqa"
            echo "Using QA service principal: AzureDevops-Pipelines"
          else
            # Default to non-prod service connection
            echo "##vso[task.setvariable variable=serviceConnection]AzureDevops-Pipelines"
            echo "##vso[task.setvariable variable=tfStateStorageAccount]arazest1tfstatedev"
            echo "Using DEFAULT service principal: AzureDevops-Pipelines"
          fi

          echo "##[section]Configuration applied:"
          echo "Service Connection: $(serviceConnection)"
          echo "Storage Account: $(tfStateStorageAccount)"
        displayName: 'Configure Environment-Specific Service Connection'

      - bash: |
          if [ ! -d "environments/$(environment)/workspaces/$(workspace)" ]; then
            echo "##vso[task.logissue type=error]Workspace $(workspace) does not exist in environment $(environment)"
            exit 1
          fi
        displayName: 'Validate Workspace Exists'

      - task: TerraformInstaller@0
        inputs:
          terraformVersion: $(TF_VERSION)
        displayName: 'Install Terraform'

      - script: |
          curl -s https://raw.githubusercontent.com/terraform-linters/tflint/master/install_linux.sh | bash
          tflint --version
        displayName: 'Install TFLint'

      - script: |
          tflint --init
          tflint --config $(WORKING_DIR)/.tflint.hcl --chdir=$(WORKING_DIR)/environments/$(environment)/workspaces/$(workspace)
        displayName: 'Run TFLint'

      - task: UsePythonVersion@0
        inputs:
          versionSpec: '3.x'
          addToPath: true

      - script: |
          pip install pre-commit
          pre-commit run --config .pre-commit-config.yml --files environments/$(environment)/workspaces/$(workspace)/*
        displayName: 'Run pre-commit checks'
        continueOnError: false



      - task: TerraformTaskV3@3
        name: terraformInit
        inputs:
          provider: 'azurerm'
          command: 'init'
          workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
          backendServiceArm: $(serviceConnection)
          backendAzureRmResourceGroupName: 'ar-az-est1-tfstate-rg'
          backendAzureRmStorageAccountName: $(tfStateStorageAccount)
          backendAzureRmContainerName: 'tfstate'
          backendAzureRmKey: 'terraform.tfstate'
        displayName: 'Terraform Init'

      - task: TerraformTaskV3@3
        name: terraformWorkspace
        inputs:
          provider: 'azurerm'
          command: 'custom'
          customCommand: 'workspace'
          commandOptions: 'select $(workspace) || terraform workspace new $(workspace)'
          workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
          environmentServiceNameAzureRM: $(serviceConnection)
        displayName: 'Select/Create Terraform Workspace'

      - task: TerraformTaskV3@3
        name: terraformValidate
        inputs:
          provider: 'azurerm'
          command: 'validate'
          workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
        displayName: 'Terraform Validate'

      - task: TerraformTaskV3@3
        name: terraformPlan
        inputs:
          provider: 'azurerm'
          command: 'plan'
          workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
          environmentServiceNameAzureRM: $(serviceConnection)
          commandOptions: '-out=$(workspace)-$(environment).tfplan'
        displayName: 'Terraform Plan'


- stage: Approval
  displayName: 'Manual Approval'
  pool: server
  condition: always()
  jobs:
  - job: WaitForApproval
    steps:
    - task: ManualValidation@0
      inputs:
        notifyUsers: |
          <EMAIL>
        instructions: 'Please validate the merge for $(environment) before proceeding with the deployment.'
        onTimeout: 'reject'